{"family": "tukxi-api-staging", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::111311033809:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::111311033809:role/ecsTaskRole", "containerDefinitions": [{"name": "tukxi-api", "image": "111311033809.dkr.ecr.ap-south-1.amazonaws.com/api:REPLACE_WITH_BUILD_NUMBER", "portMappings": [{"containerPort": 3000, "protocol": "tcp"}], "essential": true, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/tukxi-api-staging", "awslogs-region": "ap-south-1", "awslogs-stream-prefix": "ecs"}}, "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "PORT", "value": "3000"}], "secrets": [{"name": "DATABASE_URL", "valueFrom": "arn:aws:secretsmanager:ap-south-1:111311033809:secret:tukxi/staging/database-url"}, {"name": "REDIS_URL", "valueFrom": "arn:aws:secretsmanager:ap-south-1:111311033809:secret:tukxi/staging/redis-url"}, {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:ap-south-1:111311033809:secret:tukxi/staging/jwt-secret"}], "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}]}